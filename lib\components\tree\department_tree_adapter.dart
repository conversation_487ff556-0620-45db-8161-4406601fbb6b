import 'package:octasync_client/models/department/department_model.dart';
import 'tree_node_data.dart';

/// 部门数据适配器
/// 让 DepartmentModel 实现 TreeNodeData 接口
class DepartmentTreeAdapter implements TreeNodeData {
  final DepartmentModel department;

  DepartmentTreeAdapter(this.department);

  @override
  String? get id => department.id;

  @override
  String get name => department.departmentName;

  @override
  String? get parentId => department.parentId;

  @override
  List<String> get parentIdList => department.parentIdList;
}

/// 部门树构建工具类
class DepartmentTreeBuilder {
  /// 将部门列表转换为树节点结构
  static List<AppTreeNode<DepartmentTreeAdapter>> buildTree(List<DepartmentModel> departments) {
    // 预分配容量以提高性能
    final nodeMap = <String, AppTreeNode<DepartmentTreeAdapter>>{};
    final rootNodes = <AppTreeNode<DepartmentTreeAdapter>>[];

    // 第一遍：创建所有节点
    for (final dept in departments) {
      if (dept.id?.isNotEmpty == true) {
        final adapter = DepartmentTreeAdapter(dept);
        final node = AppTreeNode<DepartmentTreeAdapter>(
          data: adapter,
          children: <AppTreeNode<DepartmentTreeAdapter>>[], // 创建可变列表
        );
        nodeMap[dept.id!] = node;
      }
    }

    // 第二遍：构建父子关系
    for (final dept in departments) {
      if (dept.id?.isNotEmpty != true) continue;

      final currentNode = nodeMap[dept.id!];
      if (currentNode == null) continue;

      if (dept.parentId?.isNotEmpty == true) {
        // 子节点：添加到父节点
        final parentNode = nodeMap[dept.parentId!];
        parentNode?.children.add(currentNode);
      } else {
        // 根节点
        rootNodes.add(currentNode);
      }
    }

    // 第三遍：按名称排序（可选）
    _sortNodesByName(rootNodes);

    return rootNodes;
  }

  /// 递归按名称排序节点
  static void _sortNodesByName(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    // 对当前层级的节点按名称排序
    nodes.sort((a, b) => a.data.name.compareTo(b.data.name));

    // 递归排序子节点
    for (final node in nodes) {
      if (node.children.isNotEmpty) {
        _sortNodesByName(node.children);
      }
    }
  }

  /// 从树节点中提取部门数据
  static List<DepartmentModel> extractDepartments(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    final departments = <DepartmentModel>[];
    _extractDepartmentsRecursive(nodes, departments);
    return departments;
  }

  /// 递归提取部门数据
  static void _extractDepartmentsRecursive(
    List<AppTreeNode<DepartmentTreeAdapter>> nodes,
    List<DepartmentModel> departments,
  ) {
    for (final node in nodes) {
      departments.add(node.data.department);
      _extractDepartmentsRecursive(node.children, departments);
    }
  }

  /// 从树节点中提取选中的部门数据
  static List<DepartmentModel> extractCheckedDepartments(
    List<AppTreeNode<DepartmentTreeAdapter>> nodes,
  ) {
    final departments = <DepartmentModel>[];
    _extractCheckedDepartmentsRecursive(nodes, departments);
    return departments;
  }

  /// 递归提取选中的部门数据
  static void _extractCheckedDepartmentsRecursive(
    List<AppTreeNode<DepartmentTreeAdapter>> nodes,
    List<DepartmentModel> departments,
  ) {
    for (final node in nodes) {
      if (node.isChecked) {
        departments.add(node.data.department);
      }
      _extractCheckedDepartmentsRecursive(node.children, departments);
    }
  }
}
