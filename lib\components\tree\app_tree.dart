import 'package:flutter/material.dart';
import 'tree_node_data.dart';
import 'app_tree_style.dart';

/// 通用树组件
/// 支持任何实现了 TreeNodeData 接口的数据类型
class AppTree<T extends TreeNodeData> extends StatefulWidget {
  /// 树节点数据列表
  final List<AppTreeNode<T>> nodes;

  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(T data)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(T data, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 树组件样式配置
  final AppTreeStyle style;

  /// 加载状态
  final bool isLoading;

  /// 错误信息
  final String? errorMessage;

  /// 重试回调
  final VoidCallback? onRetry;

  /// 空数据提示文本
  final String emptyDataMessage;

  /// 加载中提示文本
  final String loadingMessage;

  /// 无搜索结果提示文本
  final String noSearchResultMessage;

  /// 重试按钮文本
  final String retryButtonText;

  AppTree({
    super.key,
    required this.nodes,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    AppTreeStyle? style,
    this.isLoading = false,
    this.errorMessage,
    this.onRetry,
    this.emptyDataMessage = '暂无数据',
    this.loadingMessage = '正在加载数据...',
    this.noSearchResultMessage = '暂无数据',
    this.retryButtonText = '重试',
  }) : style = style ?? AppTreeStyle.defaultStyle();

  @override
  State<AppTree<T>> createState() => _AppTreeState<T>();
}

class _AppTreeState<T extends TreeNodeData> extends State<AppTree<T>> {
  late List<AppTreeNode<T>> _treeNodes;
  String? _currentSearchQuery;

  // 性能优化：缓存节点映射
  final Map<String, AppTreeNode<T>> _nodeMap = {};

  @override
  void initState() {
    super.initState();
    _treeNodes = widget.nodes;
    _currentSearchQuery = widget.searchQuery;
    _buildNodeMap();
    _applySearchFilter();
  }

  @override
  void didUpdateWidget(AppTree<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查数据是否发生变化
    if (oldWidget.nodes != widget.nodes) {
      _treeNodes = widget.nodes;
      _buildNodeMap();
    }

    // 检查搜索查询是否发生变化
    if (oldWidget.searchQuery != widget.searchQuery) {
      _currentSearchQuery = widget.searchQuery;
      _applySearchFilter();
    }
  }

  /// 构建节点映射缓存
  void _buildNodeMap() {
    _nodeMap.clear();
    _buildNodeMapRecursive(_treeNodes);
  }

  void _buildNodeMapRecursive(List<AppTreeNode<T>> nodes) {
    for (final node in nodes) {
      if (node.data.id?.isNotEmpty == true) {
        _nodeMap[node.data.id!] = node;
      }
      _buildNodeMapRecursive(node.children);
    }
  }

  /// 应用搜索过滤
  void _applySearchFilter() {
    if (_currentSearchQuery == null || _currentSearchQuery!.trim().isEmpty) {
      // 没有搜索条件，显示所有节点
      _setAllNodesVisible(_treeNodes, true);
      return;
    }

    final query = _currentSearchQuery!.trim().toLowerCase();
    _filterNodesRecursive(_treeNodes, query);
  }

  /// 设置所有节点的可见性
  void _setAllNodesVisible(List<AppTreeNode<T>> nodes, bool visible) {
    for (final node in nodes) {
      node.isVisible = visible;
      _setAllNodesVisible(node.children, visible);
    }
  }

  /// 递归过滤节点
  bool _filterNodesRecursive(List<AppTreeNode<T>> nodes, String query) {
    bool hasVisibleChild = false;

    for (final node in nodes) {
      // 检查当前节点是否匹配搜索条件
      final nodeMatches = node.data.displayText.toLowerCase().contains(query);

      // 递归检查子节点
      final childrenVisible = _filterNodesRecursive(node.children, query);

      // 如果当前节点匹配或有可见的子节点，则显示当前节点
      node.isVisible = nodeMatches || childrenVisible;

      // 如果当前节点可见，则父节点也应该可见
      if (node.isVisible) {
        hasVisibleChild = true;
        // 如果有匹配的子节点，自动展开当前节点
        if (childrenVisible && !nodeMatches) {
          node.isExpanded = true;
        }
      }
    }

    return hasVisibleChild;
  }

  @override
  Widget build(BuildContext context) {
    // 错误状态
    if (widget.errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(
              widget.errorMessage!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.error),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (widget.onRetry != null)
              ElevatedButton(onPressed: widget.onRetry, child: Text(widget.retryButtonText)),
          ],
        ),
      );
    }

    // 加载状态
    if (widget.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(widget.loadingMessage, style: Theme.of(context).textTheme.labelMedium),
          ],
        ),
      );
    }

    // 空数据状态
    if (_treeNodes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.folder_open, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(widget.emptyDataMessage, style: const TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    // 正常内容
    return _buildTreeContent(context);
  }

  /// 构建树内容，处理搜索无结果的情况
  Widget _buildTreeContent(BuildContext context) {
    final treeWidgets = _buildTreeNodes(_treeNodes);

    // 检查是否有搜索条件且无搜索结果
    if (_currentSearchQuery != null &&
        _currentSearchQuery!.trim().isNotEmpty &&
        treeWidgets.isEmpty) {
      return Center(
        child: Text(widget.noSearchResultMessage, style: Theme.of(context).textTheme.labelMedium),
      );
    }

    return ListView(padding: widget.style.listViewPadding, children: treeWidgets);
  }

  /// 构建树节点列表
  List<Widget> _buildTreeNodes(List<AppTreeNode<T>> nodes) {
    List<Widget> widgets = [];

    for (var node in nodes) {
      // 只构建可见的节点
      if (node.isVisible) {
        widgets.add(_buildTreeNodeWidget(node));

        // 如果节点展开且有子节点，递归构建子节点
        if (node.isExpanded && node.children.isNotEmpty) {
          widgets.addAll(_buildTreeNodes(node.children));
        }
      }
    }

    return widgets;
  }

  /// 构建单个树节点组件
  Widget _buildTreeNodeWidget(AppTreeNode<T> node) {
    return Container(
      margin: widget.style.nodeContainerMargin(node.level),
      child: Material(
        color: widget.style.nodeMaterialColor(context, node.isSelected),
        borderRadius: widget.style.nodeBorderRadius,
        child: InkWell(
          onTap: () => _handleNodeTap(node),
          hoverColor: widget.style.nodeHoverColor(context),
          splashColor: widget.style.nodeSplashColor(context),
          borderRadius: widget.style.nodeBorderRadius,
          child: Container(
            height: widget.style.nodeHeight,
            padding: widget.style.nodeContainerPadding,
            child: Row(
              children: [
                _buildExpandIcon(node),
                widget.style.iconCheckboxSpacing,
                if (widget.showCheckbox) ..._buildCheckboxSection(node),
                _buildNodeText(node),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建展开/折叠图标
  Widget _buildExpandIcon(AppTreeNode<T> node) {
    return SizedBox(
      width: widget.style.expandIconSize.width,
      height: widget.style.expandIconSize.height,
      child:
          node.children.isNotEmpty
              ? GestureDetector(
                onTap: () => _handleNodeToggle(node),
                child: Center(
                  child: AnimatedRotation(
                    turns: node.isExpanded ? 0 : -0.25,
                    duration: widget.style.expandAnimationDuration,
                    child: Icon(
                      widget.style.expandIcon,
                      size: widget.style.expandIconSizeValue,
                      color: widget.style.expandIconColor(context),
                    ),
                  ),
                ),
              )
              : null, // 叶子节点不显示图标，但保持占位空间
    );
  }

  /// 构建复选框区域
  List<Widget> _buildCheckboxSection(AppTreeNode<T> node) {
    return [
      SizedBox(
        height: widget.style.checkboxHeight,
        child: Checkbox(
          value: node.isIndeterminate ? null : node.isChecked,
          tristate: true, // 启用三态支持
          onChanged: (value) => _handleNodeCheckboxChanged(node, value),
        ),
      ),
      widget.style.checkboxTextSpacing,
    ];
  }

  /// 构建节点文本
  Widget _buildNodeText(AppTreeNode<T> node) {
    return Expanded(
      child: Align(
        alignment: widget.style.textAlignment,
        child: Text(
          node.data.displayText,
          style: widget.style.nodeTextStyle(context, node.isSelected),
          overflow: widget.style.textOverflow,
          maxLines: widget.style.textMaxLines,
        ),
      ),
    );
  }

  /// 处理节点点击事件（仅影响高亮显示，不影响复选框状态）
  void _handleNodeTap(AppTreeNode<T> node) {
    try {
      if (!mounted) return;

      setState(() {
        // 如果有之前选中的节点，取消选中（仅影响高亮显示）
        _clearAllHighlightSelections(_treeNodes);
        // 选中当前节点（仅高亮显示）
        node.isSelected = true;
      });

      // 触发回调
      widget.onNodeTap?.call(node.data);
    } catch (error) {
      debugPrint('处理节点点击时发生错误: $error');
    }
  }

  /// 处理节点展开/折叠
  void _handleNodeToggle(AppTreeNode<T> node) {
    setState(() {
      node.isExpanded = !node.isExpanded;
    });
  }

  /// 处理复选框选择
  void _handleNodeCheckboxChanged(AppTreeNode<T> node, bool? value) {
    try {
      if (!mounted) return;

      setState(() {
        final newCheckedState = value ?? false;

        // 设置当前节点的选中状态
        node.isChecked = newCheckedState;
        node.isIndeterminate = false; // 直接操作时清除半选状态

        // 级联选择子节点
        _cascadeCheckChildren(node, newCheckedState);

        // 更新所有父节点的状态
        _updateParentCheckStates();
      });

      // 触发回调，传递当前节点的选中状态
      widget.onNodeSelected?.call(node.data, node.isChecked);
    } catch (error) {
      debugPrint('处理复选框选择时发生错误: $error');
    }
  }

  /// 清除所有节点的高亮选择状态
  void _clearAllHighlightSelections(List<AppTreeNode<T>> nodes) {
    for (final node in nodes) {
      node.isSelected = false;
      _clearAllHighlightSelections(node.children);
    }
  }

  /// 级联选择子节点
  void _cascadeCheckChildren(AppTreeNode<T> node, bool checked) {
    // 使用迭代而非递归，避免栈溢出
    final stack = <AppTreeNode<T>>[...node.children];

    while (stack.isNotEmpty) {
      final child = stack.removeLast();
      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache(); // 清除缓存

      // 添加子节点到栈中
      stack.addAll(child.children);
    }
  }

  /// 更新所有父节点的选中状态
  void _updateParentCheckStates() {
    // 从叶子节点向上更新，避免重复计算
    final processedNodes = <String>{};

    for (final node in _nodeMap.values) {
      if (node.isLeaf && !processedNodes.contains(node.data.id)) {
        _updateAncestorStates(node, processedNodes);
      }
    }
  }

  /// 更新祖先节点的状态
  void _updateAncestorStates(AppTreeNode<T> node, Set<String> processedNodes) {
    // 查找父节点
    final parentId = node.data.parentId;
    if (parentId == null || parentId.isEmpty) return;

    final parentNode = _nodeMap[parentId];
    if (parentNode == null || processedNodes.contains(parentId)) return;

    // 标记为已处理
    processedNodes.add(parentId);

    // 计算父节点的状态
    final checkedCount = parentNode.checkedChildrenCount;
    final totalChildren = parentNode.children.length;

    if (checkedCount == 0) {
      // 没有子节点被选中
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
    } else if (checkedCount == totalChildren && !parentNode.hasPartiallyCheckedChildren) {
      // 所有子节点都被选中且没有半选状态的子节点
      parentNode.isChecked = true;
      parentNode.isIndeterminate = false;
    } else {
      // 部分子节点被选中或有半选状态的子节点
      parentNode.isChecked = false;
      parentNode.isIndeterminate = true;
    }

    parentNode.clearCache(); // 清除缓存

    // 递归更新祖先节点
    _updateAncestorStates(parentNode, processedNodes);
  }

  /// 重置所有复选框状态
  void resetAllNodesCheck() {
    if (!mounted) return;

    setState(() {
      _resetNodesCheckState(_treeNodes);
    });
  }

  /// 递归重置节点复选框状态
  void _resetNodesCheckState(List<AppTreeNode<T>> nodes) {
    for (final node in nodes) {
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
      _resetNodesCheckState(node.children);
    }
  }

  /// 切换单个节点的复选框状态
  void toggleNodeCheck(String nodeId, {bool? forceState}) {
    try {
      if (nodeId.isEmpty) {
        debugPrint('警告: 尝试切换空的节点ID');
        return;
      }

      final node = _nodeMap[nodeId];
      if (node == null) {
        debugPrint('警告: 未找到ID为 $nodeId 的节点');
        return;
      }

      if (!mounted) return;

      setState(() {
        // 如果指定了强制状态，使用指定状态；否则切换当前状态
        final newCheckedState = forceState ?? !node.isChecked;

        // 设置当前节点的选中状态
        node.isChecked = newCheckedState;
        node.isIndeterminate = false; // 直接操作时清除半选状态

        // 级联选择子节点
        _cascadeCheckChildren(node, newCheckedState);

        // 更新所有父节点的状态
        _updateParentCheckStates();
      });

      // 触发回调，传递当前节点的选中状态
      widget.onNodeSelected?.call(node.data, node.isChecked);
    } catch (error) {
      debugPrint('切换节点复选框状态时发生错误: $error');
    }
  }

  /// 获取所有选中的节点数据
  List<T> getCheckedNodesData() {
    final checkedNodes = <T>[];
    _collectCheckedNodes(_treeNodes, checkedNodes);
    return checkedNodes;
  }

  /// 递归收集选中的节点数据
  void _collectCheckedNodes(List<AppTreeNode<T>> nodes, List<T> checkedNodes) {
    for (final node in nodes) {
      if (node.isChecked) {
        checkedNodes.add(node.data);
      }
      _collectCheckedNodes(node.children, checkedNodes);
    }
  }
}
