# 通用树组件 (AppTree)

这是一个通用的、可复用的树形组件，支持任意数据类型，提供了丰富的功能和高度的可定制性。

## 特性

- ✅ **通用性**: 支持任意实现了 `TreeNodeData` 接口的数据类型
- ✅ **展开/折叠**: 支持节点的展开和折叠功能
- ✅ **复选框选择**: 可选的复选框支持，包括级联选择和半选状态
- ✅ **搜索过滤**: 内置搜索功能，支持节点过滤和自动展开
- ✅ **高亮选择**: 支持节点点击高亮显示
- ✅ **样式定制**: 完全可定制的样式系统
- ✅ **状态管理**: 内置加载、错误、空数据状态处理
- ✅ **性能优化**: 使用缓存和迭代算法优化性能

## 快速开始

### 1. 创建数据模型

首先，创建一个实现 `TreeNodeData` 接口的数据类：

```dart
class MyData implements TreeNodeData {
  final String _id;
  final String _name;
  final String? _parentId;
  final List<String> _parentIdList;

  MyData(this._id, this._name, this._parentId, this._parentIdList);

  @override
  String? get id => _id;

  @override
  String get name => _name;

  @override
  String? get parentId => _parentId;

  @override
  List<String> get parentIdList => _parentIdList;
}
```

### 2. 构建树节点

创建树节点结构：

```dart
final nodes = [
  AppTreeNode<MyData>(
    data: MyData('1', '根节点', null, []),
    children: [
      AppTreeNode<MyData>(
        data: MyData('2', '子节点1', '1', ['1']),
      ),
      AppTreeNode<MyData>(
        data: MyData('3', '子节点2', '1', ['1']),
      ),
    ],
  ),
];
```

### 3. 使用树组件

```dart
AppTree<MyData>(
  nodes: nodes,
  showCheckbox: true,
  onNodeTap: (data) {
    print('点击了: ${data.displayText}');
  },
  onNodeSelected: (data, isSelected) {
    print('${data.displayText} ${isSelected ? '被选中' : '被取消选中'}');
  },
)
```

## 高级用法

### 自定义样式

```dart
AppTree<MyData>(
  nodes: nodes,
  style: AppTreeStyle.defaultStyle().copyWith(
    nodeHeight: 48.0,
    indentPerLevel: 32.0,
    nodeTextStyle: (context, isSelected) => TextStyle(
      color: isSelected ? Colors.blue : Colors.black,
      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
    ),
  ),
)
```

### 搜索功能

```dart
String searchQuery = '';

AppTree<MyData>(
  nodes: nodes,
  searchQuery: searchQuery.isEmpty ? null : searchQuery,
)
```

### 状态处理

```dart
AppTree<MyData>(
  nodes: nodes,
  isLoading: isLoading,
  errorMessage: errorMessage,
  onRetry: () => loadData(),
  loadingMessage: '正在加载...',
  emptyDataMessage: '暂无数据',
)
```

## API 参考

### AppTree 属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `nodes` | `List<AppTreeNode<T>>` | 必需 | 树节点数据列表 |
| `showCheckbox` | `bool` | `false` | 是否显示复选框 |
| `onNodeTap` | `Function(T)?` | `null` | 节点点击回调 |
| `onNodeSelected` | `Function(T, bool)?` | `null` | 节点选择回调 |
| `searchQuery` | `String?` | `null` | 搜索查询字符串 |
| `isLoading` | `bool` | `false` | 加载状态 |
| `errorMessage` | `String?` | `null` | 错误信息 |
| `onRetry` | `VoidCallback?` | `null` | 重试回调 |
| `style` | `AppTreeStyle` | 默认样式 | 样式配置 |

### AppTreeNode 属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `data` | `T` | 必需 | 节点数据 |
| `children` | `List<AppTreeNode<T>>` | `[]` | 子节点列表 |
| `isExpanded` | `bool` | `false` | 是否展开 |
| `isSelected` | `bool` | `false` | 是否被选中（高亮） |
| `isChecked` | `bool` | `false` | 复选框是否选中 |
| `isIndeterminate` | `bool` | `false` | 复选框是否半选 |
| `isVisible` | `bool` | `true` | 是否可见（搜索时使用） |

### TreeNodeData 接口

```dart
abstract class TreeNodeData {
  String? get id;              // 节点唯一标识符
  String get name;             // 节点名称
  String? get parentId;        // 父节点ID
  List<String> get parentIdList; // 父节点ID列表（用于计算层级）
}
```

## 部门选择器示例

原有的部门选择器已经重构为使用通用树组件：

```dart
DepartmentTree(
  showCheckbox: true,
  onNodeTap: (department) {
    print('选择了部门: ${department.departmentName}');
  },
  onNodeSelected: (department, isSelected) {
    print('部门 ${department.departmentName} ${isSelected ? '被选中' : '被取消选中'}');
  },
)
```

## 性能优化

- 使用节点映射缓存提高查找性能
- 使用迭代算法避免深度递归导致的栈溢出
- 缓存计算结果（如层级深度、选中数量等）
- 只渲染可见节点，提高大数据量时的性能

## 注意事项

1. 确保每个节点的 `id` 是唯一的
2. `parentIdList` 应该包含从根节点到当前节点父节点的所有ID
3. 搜索功能会自动展开包含匹配结果的父节点
4. 复选框的级联选择会自动处理父子节点的状态同步

## 更多示例

查看 `example/tree_example.dart` 文件获取完整的使用示例，包括文件系统树的实现。
