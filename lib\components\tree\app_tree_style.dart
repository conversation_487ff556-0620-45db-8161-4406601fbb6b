import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 通用树组件样式配置类
class AppTreeStyle {
  /// 列表视图内边距
  final EdgeInsets listViewPadding;

  /// 每层级的缩进距离
  final double indentPerLevel;

  /// 节点容器底部间距
  final double nodeBottomMargin;

  /// 节点材质圆角
  final BorderRadius nodeBorderRadius;

  /// 节点容器内边距
  final EdgeInsets nodeContainerPadding;

  /// 节点固定高度
  final double nodeHeight;

  /// 展开/折叠图标区域尺寸
  final Size expandIconSize;

  /// 展开/折叠图标
  final IconData expandIcon;

  /// 展开/折叠图标大小
  final double expandIconSizeValue;

  /// 展开/折叠动画持续时间
  final Duration expandAnimationDuration;

  /// 图标与复选框之间的间距
  final Widget iconCheckboxSpacing;

  /// 复选框高度
  final double checkboxHeight;

  /// 复选框与文本之间的间距
  final Widget checkboxTextSpacing;

  /// 文本溢出处理
  final TextOverflow textOverflow;

  /// 文本最大行数
  final int textMaxLines;

  /// 文本对齐方式
  final Alignment textAlignment;

  /// 节点材质背景色回调
  final Color Function(BuildContext context, bool isSelected) nodeMaterialColor;

  /// 节点悬停颜色回调
  final Color Function(BuildContext context) nodeHoverColor;

  /// 节点点击水波纹颜色回调
  final Color Function(BuildContext context) nodeSplashColor;

  /// 展开/折叠图标颜色回调
  final Color Function(BuildContext context) expandIconColor;

  /// 节点文本样式回调
  final TextStyle Function(BuildContext context, bool isSelected) nodeTextStyle;

  const AppTreeStyle({
    this.listViewPadding = const EdgeInsets.all(8.0),
    this.indentPerLevel = 20.0,
    this.nodeBottomMargin = 2.0,
    this.nodeBorderRadius = const BorderRadius.all(Radius.circular(4.0)),
    this.nodeContainerPadding = const EdgeInsets.symmetric(horizontal: 8.0),
    this.nodeHeight = 36.0,
    this.expandIconSize = const Size(20, 20),
    this.expandIcon = Icons.keyboard_arrow_down,
    this.expandIconSizeValue = 16.0,
    this.expandAnimationDuration = const Duration(milliseconds: 300),
    this.iconCheckboxSpacing = const SizedBox(width: 0),
    this.checkboxHeight = 20.0,
    this.checkboxTextSpacing = const SizedBox(width: 0),
    this.textOverflow = TextOverflow.ellipsis,
    this.textMaxLines = 1,
    this.textAlignment = Alignment.centerLeft,
    required this.nodeMaterialColor,
    required this.nodeHoverColor,
    required this.nodeSplashColor,
    required this.expandIconColor,
    required this.nodeTextStyle,
  });

  /// 创建默认样式（基于原部门树样式）
  static AppTreeStyle defaultStyle() {
    return AppTreeStyle(
      listViewPadding: const EdgeInsets.all(8.0),
      indentPerLevel: 20.0,
      nodeBottomMargin: 2.0,
      nodeBorderRadius: BorderRadius.circular(AppRadiusSize.radius4),
      nodeContainerPadding: const EdgeInsets.symmetric(horizontal: 8.0),
      nodeHeight: 36.0,
      expandIconSize: const Size(20, 20),
      expandIcon: IconFont.mianxing_xiala,
      expandIconSizeValue: 16.0,
      expandAnimationDuration: const Duration(milliseconds: 300),
      iconCheckboxSpacing: const SizedBox(width: 0),
      checkboxHeight: 20.0,
      checkboxTextSpacing: const SizedBox(width: 0),
      textOverflow: TextOverflow.ellipsis,
      textMaxLines: 1,
      textAlignment: Alignment.centerLeft,
      nodeMaterialColor:
          (context, isSelected) => isSelected ? context.background200 : Colors.transparent,
      nodeHoverColor: (context) => context.activeGrayColor.withValues(alpha: 0.5),
      nodeSplashColor: (context) => context.activeGrayColor,
      expandIconColor: (context) => context.icon100,
      nodeTextStyle:
          (context, isSelected) =>
              TextStyle(color: context.textPrimary, fontWeight: FontWeight.normal, fontSize: 13),
    );
  }

  /// 获取节点容器外边距
  EdgeInsets nodeContainerMargin(int indentLevel) {
    return EdgeInsets.only(left: indentLevel * indentPerLevel, bottom: nodeBottomMargin);
  }

  /// 复制样式并修改部分属性
  AppTreeStyle copyWith({
    EdgeInsets? listViewPadding,
    double? indentPerLevel,
    double? nodeBottomMargin,
    BorderRadius? nodeBorderRadius,
    EdgeInsets? nodeContainerPadding,
    double? nodeHeight,
    Size? expandIconSize,
    IconData? expandIcon,
    double? expandIconSizeValue,
    Duration? expandAnimationDuration,
    Widget? iconCheckboxSpacing,
    double? checkboxHeight,
    Widget? checkboxTextSpacing,
    TextOverflow? textOverflow,
    int? textMaxLines,
    Alignment? textAlignment,
    Color Function(BuildContext context, bool isSelected)? nodeMaterialColor,
    Color Function(BuildContext context)? nodeHoverColor,
    Color Function(BuildContext context)? nodeSplashColor,
    Color Function(BuildContext context)? expandIconColor,
    TextStyle Function(BuildContext context, bool isSelected)? nodeTextStyle,
  }) {
    return AppTreeStyle(
      listViewPadding: listViewPadding ?? this.listViewPadding,
      indentPerLevel: indentPerLevel ?? this.indentPerLevel,
      nodeBottomMargin: nodeBottomMargin ?? this.nodeBottomMargin,
      nodeBorderRadius: nodeBorderRadius ?? this.nodeBorderRadius,
      nodeContainerPadding: nodeContainerPadding ?? this.nodeContainerPadding,
      nodeHeight: nodeHeight ?? this.nodeHeight,
      expandIconSize: expandIconSize ?? this.expandIconSize,
      expandIcon: expandIcon ?? this.expandIcon,
      expandIconSizeValue: expandIconSizeValue ?? this.expandIconSizeValue,
      expandAnimationDuration: expandAnimationDuration ?? this.expandAnimationDuration,
      iconCheckboxSpacing: iconCheckboxSpacing ?? this.iconCheckboxSpacing,
      checkboxHeight: checkboxHeight ?? this.checkboxHeight,
      checkboxTextSpacing: checkboxTextSpacing ?? this.checkboxTextSpacing,
      textOverflow: textOverflow ?? this.textOverflow,
      textMaxLines: textMaxLines ?? this.textMaxLines,
      textAlignment: textAlignment ?? this.textAlignment,
      nodeMaterialColor: nodeMaterialColor ?? this.nodeMaterialColor,
      nodeHoverColor: nodeHoverColor ?? this.nodeHoverColor,
      nodeSplashColor: nodeSplashColor ?? this.nodeSplashColor,
      expandIconColor: expandIconColor ?? this.expandIconColor,
      nodeTextStyle: nodeTextStyle ?? this.nodeTextStyle,
    );
  }
}
