import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/components/tree/app_tree.dart';
import 'package:octasync_client/components/tree/department_tree_adapter.dart';
import 'package:octasync_client/components/tree/app_tree_style.dart';
import 'package:octasync_client/components/tree/tree_node_data.dart';

/// 部门树状选择器组件
/// 重构后使用通用的 AppTree 组件，保留API调用逻辑
class DepartmentTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  const DepartmentTree({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
  });

  @override
  State<DepartmentTree> createState() => DepartmentTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentTreeState extends State<DepartmentTree> {
  final Map<String, dynamic> _reqParams = {'PageIndex': 1, 'PageSize': 20000};

  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];
  List<AppTreeNode<DepartmentTreeAdapter>> _treeNodes = [];

  // 加载状态管理
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDepartmentData();
  }

  @override
  void didUpdateWidget(DepartmentTree oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果搜索查询发生变化，不需要重新加载数据，AppTree会自动处理
    // 这里可以添加其他需要响应的属性变化
  }

  /// 加载部门数据
  Future<void> _loadDepartmentData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await DepartmentApi.getList(_reqParams);
      if (mounted) {
        setState(() {
          _pages = PagesModel.fromJson(
            response,
            (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
          );
          _list = _pages.items;
          _buildTreeStructure();
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 构建树形结构
  void _buildTreeStructure() {
    // 使用适配器构建树结构
    _treeNodes = DepartmentTreeBuilder.buildTree(_list);
  }

  /// 刷新数据
  void refresh() {
    _loadDepartmentData();
  }

  /// 获取所有选中的部门数据
  List<DepartmentModel> getAllCheckedDepartments() {
    return DepartmentTreeBuilder.extractCheckedDepartments(_treeNodes);
  }

  /// 重置所有复选框状态
  void resetAllNodesCheck() {
    setState(() {
      _resetNodesCheckState(_treeNodes);
    });
  }

  /// 递归重置节点复选框状态
  void _resetNodesCheckState(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    for (final node in nodes) {
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
      _resetNodesCheckState(node.children);
    }
  }

  /// 选中节点
  void checkNode(String nodeId) {
    final node = _findNodeById(nodeId);
    if (node != null) {
      setState(() {
        node.isChecked = true;
        node.isIndeterminate = false;
      });
    }
  }

  /// 取消选中节点
  void uncheckNode(String nodeId) {
    final node = _findNodeById(nodeId);
    if (node != null) {
      setState(() {
        node.isChecked = false;
        node.isIndeterminate = false;
      });
    }
  }

  /// 设置搜索查询（空实现，搜索由AppTree处理）
  void setSearchQuery(String query) {
    // 搜索功能由AppTree组件处理，这里不需要实现
  }

  /// 根据ID查找节点
  AppTreeNode<DepartmentTreeAdapter>? _findNodeById(String nodeId) {
    for (final node in _treeNodes) {
      final found = node.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return AppTree<DepartmentTreeAdapter>(
      nodes: _treeNodes,
      showCheckbox: widget.showCheckbox,
      searchQuery: widget.searchQuery,
      isLoading: _isLoading,
      onRetry: _loadDepartmentData,
      onNodeTap: (adapter) {
        widget.onNodeTap?.call(adapter.department);
      },
      onNodeSelected: (adapter, isSelected) {
        widget.onNodeSelected?.call(adapter.department, isSelected);
      },
    );
  }
}
