import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/components/tree/app_tree.dart';
import 'package:octasync_client/components/tree/department_tree_adapter.dart';
import 'package:octasync_client/components/tree/app_tree_style.dart';
import 'package:octasync_client/components/tree/tree_node_data.dart';

// 常量定义
class _DepartmentTreeConstants {
  static const int defaultPageSize = 9999;
  static const String loadingMessage = '正在加载部门数据...';
  static const String emptyDataMessage = '暂无部门数据';
  static const String noSearchResultMessage = '暂无数据';
  static const String retryButtonText = '重试';
  static const String loadErrorPrefix = '加载部门数据失败: ';
}

/// 部门树状选择器组件
/// 重构后使用通用的 AppTree 组件，保留API调用逻辑
class DepartmentTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  const DepartmentTree({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
  });

  @override
  State<DepartmentTree> createState() => DepartmentTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentTreeState extends State<DepartmentTree> {
  final Map<String, dynamic> _reqParams = {
    'PageIndex': 1,
    'PageSize': _DepartmentTreeConstants.defaultPageSize,
  };

  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _list = [];
  List<AppTreeNode<DepartmentTreeAdapter>> _treeNodes = [];

  // 加载状态管理
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadDepartmentData();
  }

  @override
  void didUpdateWidget(DepartmentTree oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果搜索查询发生变化，不需要重新加载数据，AppTree会自动处理
    // 这里可以添加其他需要响应的属性变化
  }

  /// 加载部门数据
  Future<void> _loadDepartmentData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await DepartmentApi.getList(_reqParams);
      if (mounted) {
        setState(() {
          _pages = PagesModel.fromJson(
            response,
            (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
          );
          _list = _pages.items;
          _buildTreeStructure();
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _errorMessage = '${_DepartmentTreeConstants.loadErrorPrefix}$error';
          _isLoading = false;
        });
      }
    }
  }

  /// 构建树形结构
  void _buildTreeStructure() {
    // 使用适配器构建树结构
    _treeNodes = DepartmentTreeBuilder.buildTree(_list);
  }

  /// 获取部门列表数据（保持向后兼容）
  @Deprecated('Use _loadDepartmentData instead')
  void getList() {
    _loadDepartmentData();
  }

  /// 刷新数据
  void refresh() {
    _loadDepartmentData();
  }

  /// 重置所有复选框状态
  void resetAllNodesCheck() {
    setState(() {
      _resetNodesCheckState(_treeNodes);
    });
  }

  /// 递归重置节点复选框状态
  void _resetNodesCheckState(List<AppTreeNode<DepartmentTreeAdapter>> nodes) {
    for (final node in nodes) {
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
      _resetNodesCheckState(node.children);
    }
  }

  /// 切换单个节点的复选框状态
  void toggleNodeCheck(String nodeId, {bool? forceState}) {
    // 查找节点
    AppTreeNode<DepartmentTreeAdapter>? targetNode;
    for (final node in _treeNodes) {
      targetNode = node.findNodeById(nodeId);
      if (targetNode != null) break;
    }

    if (targetNode == null) {
      debugPrint('警告: 未找到ID为 $nodeId 的节点');
      return;
    }

    final node = targetNode; // 已经检查过非空
    setState(() {
      // 如果指定了强制状态，使用指定状态；否则切换当前状态
      final newCheckedState = forceState ?? !node.isChecked;
      node.isChecked = newCheckedState;
      node.isIndeterminate = false;

      // 级联选择子节点
      _cascadeCheckChildren(node, newCheckedState);

      // 更新父节点状态
      _updateParentCheckStates(node);
    });

    // 触发回调
    widget.onNodeSelected?.call(node.data.department, node.isChecked);
  }

  /// 选中节点（向后兼容方法）
  void checkNode(String nodeId) {
    toggleNodeCheck(nodeId, forceState: true);
  }

  /// 取消选中节点（向后兼容方法）
  void uncheckNode(String nodeId) {
    toggleNodeCheck(nodeId, forceState: false);
  }

  /// 级联选择子节点
  void _cascadeCheckChildren(AppTreeNode<DepartmentTreeAdapter> node, bool checked) {
    final stack = <AppTreeNode<DepartmentTreeAdapter>>[...node.children];

    while (stack.isNotEmpty) {
      final child = stack.removeLast();
      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache();

      // 添加子节点到栈中
      stack.addAll(child.children);
    }
  }

  /// 更新父节点的选中状态
  void _updateParentCheckStates(AppTreeNode<DepartmentTreeAdapter> node) {
    final parentId = node.data.parentId;
    if (parentId == null || parentId.isEmpty) return;

    // 查找父节点
    AppTreeNode<DepartmentTreeAdapter>? parentNode;
    for (final rootNode in _treeNodes) {
      parentNode = rootNode.findNodeById(parentId);
      if (parentNode != null) break;
    }

    if (parentNode == null) return;

    // 计算父节点的状态
    final checkedCount = parentNode.checkedChildrenCount;
    final totalChildren = parentNode.children.length;

    if (checkedCount == 0) {
      // 没有子节点被选中
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
    } else if (checkedCount == totalChildren && !parentNode.hasPartiallyCheckedChildren) {
      // 所有子节点都被选中且没有半选状态的子节点
      parentNode.isChecked = true;
      parentNode.isIndeterminate = false;
    } else {
      // 部分子节点被选中或有半选状态的子节点
      parentNode.isChecked = false;
      parentNode.isIndeterminate = true;
    }

    parentNode.clearCache();

    // 递归更新祖先节点
    _updateParentCheckStates(parentNode);
  }

  /// 获取所有选中的部门数据（向后兼容方法）
  List<DepartmentModel> getAllCheckedDepartments() {
    return DepartmentTreeBuilder.extractCheckedDepartments(_treeNodes);
  }

  /// 获取所有选中的部门数据
  List<DepartmentModel> getCheckedDepartments() {
    return getAllCheckedDepartments();
  }

  /// 设置搜索查询（向后兼容方法）
  void setSearchQuery(String query) {
    // 这个方法现在不需要做任何事情，因为搜索由AppTree组件处理
    // 保留这个方法是为了向后兼容
  }

  @override
  Widget build(BuildContext context) {
    return AppTree<DepartmentTreeAdapter>(
      nodes: _treeNodes,
      showCheckbox: widget.showCheckbox,
      searchQuery: widget.searchQuery,
      isLoading: _isLoading,
      errorMessage: _errorMessage,
      onRetry: _loadDepartmentData,
      loadingMessage: _DepartmentTreeConstants.loadingMessage,
      emptyDataMessage: _DepartmentTreeConstants.emptyDataMessage,
      noSearchResultMessage: _DepartmentTreeConstants.noSearchResultMessage,
      retryButtonText: _DepartmentTreeConstants.retryButtonText,
      style: AppTreeStyle.defaultStyle(),
      onNodeTap: (adapter) {
        widget.onNodeTap?.call(adapter.department);
      },
      onNodeSelected: (adapter, isSelected) {
        widget.onNodeSelected?.call(adapter.department, isSelected);
      },
    );
  }
}
